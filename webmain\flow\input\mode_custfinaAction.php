<?php
/**
*	客户.收款单
*/
class mode_custfinaClassAction extends inputAction{
	
	/**
	 * 电子服务单类型常量
	 */
	const ELECTWORK_TYPE = 6;
	
	/**
	 * 管理员类型常量
	 */
	const ADMIN_TYPE_SUPER = 1;
	const ADMIN_TYPE_ADMIN = 2;
	
	/**
	 * 存储原始的payeeid值，防止被changeusercheck类型字段覆盖
	 */
	private $originalPayeeid;
	
	/**
	 * 处理电子服务单的公共逻辑
	 * @param int $electid 电子服务单ID
	 * @param array $arr 表单参数
	 * @param int $id 记录ID
	 * @param float $money 收款金额
	 * @param string $htidFormat htid格式（用于getmoneys查询）
	 * @return array
	 */
	private function processElectworkDocument($electid, $arr, $id, $money, $htidFormat)
	{
		// 通过关联查询获取客户名称和收款人信息
		$htrsArr = $this->db->getall('SELECT g.`id`,g.`num`,g.`custid`,c.`name` as custname,g.`prname`,g.`money`,g.`dist`,g.`distid` FROM `[Q]goodm` g LEFT JOIN `[Q]customer` c ON g.`custid`=c.`id` WHERE g.`id`='.$electid.' and g.`type`=6');
		
		if(!$htrsArr || count($htrsArr) == 0){
			return array('error' => '电子服务单不存在');
		}
		
		$htrs = $htrsArr[0]; // 取第一条记录
		$narr = array();
		$narr['htnum'] 		= $htrs['num'];
		$narr['custid'] 	= $htrs['custid'];
		$narr['type'] 		= '0';
		$narr['custname'] 	= $htrs['custname'];
		
		// 自动填充收款人信息：如果payee字段没有值，则自动获取电子服务单的dist到payee，distid到payeeid
		if(empty($arr['payee']) && !empty($htrs['dist'])) {
			$narr['payee'] = $htrs['dist']; // 电子服务单的收款人对应dist字段
		}
		if(empty($arr['payeeid']) && !empty($htrs['distid'])) {
			$narr['payeeid'] = $htrs['distid']; // 电子服务单的收款人ID对应distid字段
		}
		
		// 验证金额
		$zmoney = floatval($htrs['money']);
		$omoney = m('crm')->getmoneys($htidFormat, $id);
		$chaojg = $omoney + $money - $zmoney;
		if($chaojg > 0){
			return array('error' => '金额已超过电子服务单上金额'.$zmoney.'');
		}
		
		$narr['htid'] = $htidFormat;
		return array('data' => $narr);
	}
	
	protected function savebefore($table, $arr, $id, $addbo){

		$narr	= array();
		$htid 	= $arr['htid'];
		$money 	= floatval($arr['money']);
		if($money<=0)return '金额必须大于0';
		
		//编辑时判断
		if($this->rs && !$addbo){
			$xgid 	= arrvalue($this->rs,'xgid');
			$xgnum 	= arrvalue($this->rs,'xgnum');
			if($xgnum && $xgid){
				$sflow = m('flow:'.$xgnum.'')->initbase($xgnum);
				$onrs  = $sflow->getone($xgid);
				if($onrs){
					$jiner = floatval($onrs['money']);
					if($money != $jiner)return '此单据关联"'.$sflow->modename.','.$onrs['num'].'"的金额'.$onrs['money'].'不一样';
					$narr['custid'] 	= $onrs['custid'];
					$narr['custname'] 	= $onrs['custname'];
					$narr['htnum'] 		= $onrs['num'];
				}
			}
		}

		
		// 处理E/e前缀的电子服务单ID
		if(preg_match('/^[eE](\d+)$/', $htid, $matches)){
			$electid = intval($matches[1]);
			// 验证是否为电子服务单
			$electCheck = $this->db->getone('[Q]goodm', '`id`='.$electid.' and `type`=6');
			if($electCheck){
				// 统一使用小写e前缀格式，确保htid格式一致性
				$htidFormat = 'e'.$electid;
				$result = $this->processElectworkDocument($electid, $arr, $id, $money, $htidFormat);
				if(isset($result['error'])){
					return $result['error'];
				}
				$narr = array_merge($narr, $result['data']);
				if(!isset($narr['type']))$narr['type'] = 0;
				return array('rows'=> $narr);
			}else{
				return '电子服务单不存在';
			}
		}
		//从销售单读取
		else if((int)$htid<0){
			$salesId = 0-(int)$htid;
			$htrs = m('goodm')->getone('`id`='.$salesId);
			if(!$htrs){
				return '销售单不存在';
			}
			$narr['htnum'] 		= $htrs['num'];
			$narr['custid'] 	= $htrs['custid'];
			$narr['type'] 		= '0';
			$narr['custname'] 	= $htrs['custname'];
			$zmoney				= floatval($htrs['money']);
			$omoney	= m('crm')->getmoneys((int)$htid, $id);
			$chaojg	= $omoney + $money - $zmoney;
			if($chaojg>0)return '金额已超过销售单上金额'.$zmoney.'';
		}
		//从电子服务单读取 - 优先检查是否为电子服务单（数字ID方式）
		else if((int)$htid > 0){
		    $electCheck = $this->db->getone('[Q]goodm', '`id`='.(int)$htid.' and `type`=6');
		    if($electCheck){
		        $electid = (int)$htid;
		        // 使用e+ID格式进行处理，保持与系统其他地方的一致性
		        $htidFormat = 'e'.$electid;
		        $result = $this->processElectworkDocument($electid, $arr, $id, $money, $htidFormat);
		        if(isset($result['error'])){
		            return $result['error'];
		        }
		        $narr = array_merge($narr, $result['data']);
		        if(!isset($narr['type']))$narr['type'] = 0;
		        return array('rows'=> $narr);
		    }
		    //从合同读取 - 只有不是电子服务单时才检查合同
		    else{
		        $htrs = m('custract')->getone((int)$htid);
		        if(!$htrs){
		            return '合同不存在';
		        }
		        $narr['htnum']      = $htrs['num'];
		        $narr['custid']     = $htrs['custid'];
		        $narr['type']       = $htrs['type'];
		        $narr['custname']   = $htrs['custname'];
		        $zmoney             = floatval($htrs['money']);
		        $omoney = m('crm')->getmoneys($htid, $id);
		        $chaojg = $omoney + $money - $zmoney;
		        if($chaojg>0)return '金额已超过合同上金额'.$zmoney.'';
		        $narr['htid'] = $htid;
		    }
		}
		else{
			$narr['htid'] = $htid;
		}
		
		// 防止changeusercheck类型字段替换payeeid值
		// 当从电子服务单自动填充时，payeeid应该来自电子服务单的distid，而不是被payee字段的changeusercheck类型覆盖
		if(isset($narr['payeeid']) && !empty($narr['payeeid'])) {
			// 保存原始的payeeid值，稍后在saveafter中恢复
			$this->originalPayeeid = $narr['payeeid'];
		}
		
		if(!isset($narr['type']))$narr['type'] = 0;
		return array('rows'=> $narr);
	}
	
		
	protected function saveafter($table, $arr, $id, $addbo){
		$htid 	= $arr['htid'];
		
		// 恢复被changeusercheck类型字段可能覆盖的payeeid值
		if(isset($this->originalPayeeid) && !empty($this->originalPayeeid)) {
			m($table)->update(array('payeeid' => $this->originalPayeeid), $id);
		}
		
		// 检查并设置当前收款单的ispay状态
		$paydt = arrvalue($arr, 'paydt');
		if(!isempt($paydt)) {
			// 更新当前收款单的ispay状态为1（已付款）
			m($table)->update(array('ispay' => 1), $id);
			
			// 强制提交事务，确保数据在调用updateElectworkPayStatus之前已写入数据库
			$this->db->query("COMMIT", false);
		}
		
		// 处理e前缀的电子服务单
		if(preg_match('/^e(\d+)$/', $htid, $matches)){
			$electid = intval($matches[1]);
			$this->updateLinkedDocumentPayStatus($electid);
		}
		//处理正数ID，使用高效的类型判断
		else if((int)$htid>0){
			$this->updateLinkedDocumentPayStatus((int)$htid);
		}
		//处理销售单
		else if((int)$htid<0){
			$salesId = 0-(int)$htid;
			$htrs = m('goodm')->getone('`id`='.$salesId);
			if($htrs) {
				m('crm')->xiaozhuantai($htrs,1);
			}
		}
	}
	
	/**
	 * 更新关联单据的收款状态（合同或电子服务单）
	 * @param int $docId 文档ID
	 */
	private function updateLinkedDocumentPayStatus($docId){
		// 先检查是否为电子服务单（type=6）
		$electCheck = $this->db->getone('[Q]goodm', '`id`='.$docId.' and `type`=6', 'id,money');
		if($electCheck){
			// 是电子服务单
			$this->updateElectworkPayStatus($docId, floatval($electCheck['money']));
			return;
		}
		
		// 检查是否为合同
		$contractCheck = $this->db->getone('[Q]custract', '`id`='.$docId, 'id');
		if($contractCheck){
			// 是合同
			m('crm')->ractmoney($docId);
		}
	}
	
	/**
	 * 更新电子服务单收款状态
	 * @param int $electid 电子服务单ID
	 * @param float $totalMoney 电子服务单总金额
	 */
	private function updateElectworkPayStatus($electid, $totalMoney){
		// 直接在这里计算已收款金额，使用当前Action的数据库连接
		// electwork模块只使用e123格式
		$condition = '(`htid`=\'e'.$electid.'\' or `htid`=\'E'.$electid.'\') and `ispay`=1';
		
		// 使用当前Action的数据库连接查询
		$sql = "SELECT id,money,htid,ispay FROM [Q]custfina WHERE $condition";
		$shouMoney = $this->db->getall($sql);
		
		$totalShou = 0;
		
		// 计算E前缀格式收款记录
		foreach($shouMoney as $sm){
			$totalShou += floatval($sm['money']);
		}
		
		// 更新ispay状态：0-未创建收款，1-已收全款，2-部分收款
		$ispay = 0; // 默认未创建收款
		if($totalShou > 0) {
			if($totalShou >= $totalMoney) {
				$ispay = 1; // 已收全款
			} else {
				$ispay = 2; // 部分收款
			}
		}
		$result = m('goodm')->update('`ispay`='.$ispay, $electid);
		
		return $result;
	}
	
	public function selectcust()
	{
		$rows = m('crm')->getmycust($this->adminid, $this->rock->arrvalue($this->rs, 'custid'));
		return $rows;
	}
	
	public function hetongdata()
	{
		$htid = $this->get('htid', '0');
		$arr = [];
		
		// 添加默认选项
		$arr[] = [
			'value' => '0',
			'name' => '不选择',
		];
		
		// 获取合同数据
		$this->addContractOptions($arr);
		
		// 获取销售单数据
		$this->addSalesOptions($arr, $htid);
		
		// 获取电子服务单数据
		$this->addElectworkOptions($arr, $htid);
		
		return $arr;
	}
	
	/**
	 * 添加合同选项
	 * @param array &$arr
	 */
	private function addContractOptions(&$arr)
	{
		// 构建权限条件
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		$condition = '`status`=1 and (`ispay`=0 or `ispay`=2)';
		
		// 如果不是管理员，只能看自己的合同
		if (!$this->isAdmin($adminInfo['type'])) {
			$condition = '`uid`=' . $this->adminid . ' and ' . $condition;
		}
		
		$rows = m('custract')->getall($condition);
		foreach ($rows as $rs) {
			$arr[] = [
				'value' => $rs['id'],
				'optgroup' => '合同',
				'name' => '[' . $rs['num'] . ']' . $rs['custname'],
			];
		}
	}
	
	/**
	 * 添加销售单选项
	 * @param array &$arr
	 * @param int $htid
	 */
	private function addSalesOptions(&$arr, $htid)
	{
		// 构建权限条件
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		$condition = '`type`=2 and `status`=1 and `custractid`=0 and (`ispay`=0 or `id`=' . (0 - $htid) . ')';
		
		// 如果不是管理员，只能看自己的销售单
		if (!$this->isAdmin($adminInfo['type'])) {
			$condition = '`uid`=' . $this->adminid . ' and ' . $condition;
		}
		
		$rows = m('goodm')->getall($condition);
		
		foreach ($rows as $rs) {
			$arr[] = [
				'value' => '-' . $rs['id'],
				'optgroup' => '销售单',
				'name' => '[' . $rs['num'] . ']' . $rs['custname'],
			];
		}
	}
	
	/**
	 * 添加电子服务单选项
	 * @param array &$arr
	 * @param int $htid
	 */
	private function addElectworkOptions(&$arr, $htid)
	{
		// 检查当前编辑的记录是否为电子服务单
		$electhtid = $this->getElectworkId($htid);
		
		// 构建用户权限条件
		$userWhere = $this->buildUserPermissionCondition();
		
		// 查询电子服务单数据
		$electrows = $this->getElectworkList($electhtid, $userWhere);
		
		// 添加到选项数组
		foreach ($electrows as $rs) {
			$displayName = '[' . $rs['num'] . ']';
			if (!empty($rs['prname'])) {
				$displayName .= $rs['prname'];
			}
			
			$arr[] = [
				'value' => 'e' . $rs['id'], // 使用e前缀来区分电子服务单
				'optgroup' => '电子服务单',
				'name' => $displayName,
			];
		}
	}
	
	/**
	 * 获取电子服务单ID（如果当前记录是电子服务单）
	 * @param mixed $htid
	 * @return int
	 */
	private function getElectworkId($htid)
	{
		// 处理E/e前缀的电子服务单ID
		if(preg_match('/^[eE](\d+)$/', $htid, $matches)){
			$electid = intval($matches[1]);
			$electCheck = $this->db->getone('[Q]goodm', '`id`=' . $electid . ' and `type`=' . self::ELECTWORK_TYPE);
			if ($electCheck) {
				return $electid;
			}
		}
		// 处理数字ID
		else if ($htid > 0) {
			$electCheck = $this->db->getone('[Q]goodm', '`id`=' . $htid . ' and `type`=' . self::ELECTWORK_TYPE);
			if ($electCheck) {
				return $htid;
			}
		}
		return 0;
	}
	
	/**
	 * 构建用户权限查询条件
	 * @return string
	 */
	private function buildUserPermissionCondition()
	{
		$adminInfo = m('admin')->getone($this->adminid, 'type');
		
		// 管理员可以看到所有用户的电子服务单
		if ($this->isAdmin($adminInfo['type'])) {
			return '';
		}
		
		// 普通用户只能看到自己的电子服务单
		return ' and g.`uid`=' . $this->adminid;
	}
	
	/**
	 * 获取电子服务单列表
	 * 过滤掉已收全款的电子服务单
	 * @param int $electhtid
	 * @param string $userWhere
	 * @return array
	 */
	private function getElectworkList($electhtid, $userWhere)
	{
		$sql = 'SELECT g.`id`, g.`num`, g.`prname`, g.`custid`, c.`name` as custname, g.`ispay`,
					g.`money` as total_money,
					COALESCE((
						SELECT SUM(cf.money) 
						FROM `[Q]custfina` cf 
						WHERE cf.ispay = "1" 
						AND (
							cf.htid = CAST(g.id AS CHAR) 
							OR cf.htid = CONCAT("-electwork_", g.id)
							OR cf.htid = CONCAT("e", g.id)
							OR cf.htid = CONCAT("E", g.id)
						)
					), 0) as paid_money
				FROM `[Q]goodm` g 
				LEFT JOIN `[Q]customer` c ON g.`custid` = c.`id` 
				WHERE g.`type`=' . self::ELECTWORK_TYPE . ' 
				  and g.`status`=1 
				  and g.`money`>0 
				  and (
					  g.`id`=' . $electhtid . ' 
					  OR (
						  -- 只显示未收全款的电子服务单
						  COALESCE((
							  SELECT SUM(cf2.money) 
							  FROM `[Q]custfina` cf2 
							  WHERE cf2.ispay = "1" 
							  AND (
								  cf2.htid = CAST(g.id AS CHAR) 
								  OR cf2.htid = CONCAT("-electwork_", g.id)
								  OR cf2.htid = CONCAT("e", g.id)
								  OR cf2.htid = CONCAT("E", g.id)
							  )
						  ), 0) < g.`money`
					  )
				  )' . $userWhere;
		
		return $this->db->getall($sql);
	}
	
	public function ractchangeAjax()
	{
		$ractid = $this->get('ractid');
		$source = $this->get('source'); // 获取业务类型参数
		$cars = ['type' => '0'];
		
		try {
			// 优先使用source参数判断业务类型
			if ($source === 'electwork') {
				// 明确指定为电子服务单
				$cars = $this->handleElectworkDocument($ractid);
			}
			// 电子服务单处理（E/e前缀，向后兼容）
			elseif (preg_match('/^[eE](\d+)$/', $ractid, $matches)) {
				$electId = (int)$matches[1];
				$cars = $this->handleElectworkDocument($electId);
			}
			// 电子服务单处理（兼容旧的数字ID方式）
			elseif ($this->isElectworkDocument($ractid)) {
				$cars = $this->handleElectworkDocument($ractid);
			}
			// 销售单处理
			elseif ((int)$ractid < 0) {
				$cars = $this->handleSalesDocument($ractid);
			}
			// 合同处理
			else {
				$cars = $this->handleContractDocument($ractid);
			}
		} catch (Exception $e) {
			// 记录错误日志
			error_log("ractchangeAjax Error: " . $e->getMessage());
			$cars = $this->getEmptyDocumentData();
		}
		
		$this->returnjson($cars);
	}
	
	/**
	 * 判断是否为电子服务单
	 * @param mixed $ractid
	 * @return bool
	 */
	private function isElectworkDocument($ractid)
	{
		if ((int)$ractid <= 0) {
			return false;
		}
		
		$electCheck = $this->db->getone('[Q]goodm', '`id`=' . (int)$ractid . ' and `type`=' . self::ELECTWORK_TYPE);
		return !empty($electCheck);
	}
	
	/**
	 * 处理电子服务单数据
	 * @param mixed $ractid
	 * @return array
	 */
	private function handleElectworkDocument($ractid)
	{
		$electid = (int)$ractid;
		
		// 优化SQL查询，获取电子服务单的enddt日期和dist信息
		$sql = 'SELECT g.`id`, g.`num`, g.`custid`, c.`name` as custname, g.`prname`, g.`enddt`, g.`money`, g.`dist`, g.`distid` 
				FROM `[Q]goodm` g 
				LEFT JOIN `[Q]customer` c ON g.`custid` = c.`id` 
				WHERE g.`id` = ' . $electid;
		
		$result = $this->db->query($sql);
		$xrs = $result ? $result->fetch_assoc() : null;
		
		if ($xrs) {
			// 计算电子服务单的待创建收款金额（参考xiaozhuantai方法的逻辑）
			// electwork模块只使用e123格式
			$shou1 = 0;
			// E前缀格式
			$finrows = $this->db->getall('select * from `[Q]custfina` where (`htid`=\'e'.$electid.'\' or `htid`=\'E'.$electid.'\') and `id`<>0');
			foreach($finrows as $rs1){ $shou1 += floatval($rs1['money']); }
			$wshou1 = floatval($xrs['money']) - $shou1;
			if($wshou1 < 0) $wshou1 = 0;
			
			// 提取日期部分（不含时间）
			$signdt = !empty($xrs['enddt']) ? date('Y-m-d', strtotime($xrs['enddt'])) : '';
			
			return [
				'custid' => $xrs['custid'],
				'custname' => $xrs['custname'],
				'num' => $xrs['num'],
				'signdt' => $signdt,
				'money' => $wshou1, // 返回待创建收款金额
				// 设置收款人信息（电子服务单对应dist字段）
				'payee' => $xrs['dist'], // 电子服务单的收款人对应dist字段
				'payeeid' => $xrs['distid'] // 电子服务单的收款人ID对应distid字段
			];
		}
		
		return $this->getEmptyDocumentData();
	}
	
	/**
	 * 处理销售单数据
	 * @param mixed $ractid
	 * @return array
	 */
	private function handleSalesDocument($ractid)
	{
		$htid = $ractid;
		$xrs = m('goodm')->getone('`id`=' . (0 - (int)$htid));
		
		if ($xrs) {
			return [
				'custid' => $xrs['custid'],
				'custname' => $xrs['custname'],
				'num' => $xrs['num'],
				'signdt' => $xrs['applydt'],
				'money' => m('crm')->xiaozhuantai($xrs, 1),
				'payee' => '', // 销售单不设置收款人
				'payeeid' => '' // 销售单不设置收款人ID
			];
		}
		
		return $this->getEmptyDocumentData();
	}
	
	/**
	 * 处理合同数据
	 * @param mixed $ractid
	 * @return array
	 */
	private function handleContractDocument($ractid)
	{
		$htid = $ractid;
		$cars = m('custract')->getone($htid, 'id,custid,custname,money,type,num,signdt');
		
		if ($cars) {
			$omoney = m('crm')->getmoneys($htid);
			$cars['money'] = $cars['money'] - $omoney;
			$cars['payee'] = ''; // 合同不设置收款人
			$cars['payeeid'] = ''; // 合同不设置收款人ID
			return $cars;
		}
		
		return $this->getEmptyDocumentData();
	}
	
	/**
	 * 获取空文档数据结构
	 * @return array
	 */
	private function getEmptyDocumentData()
	{
		return [
			'custid' => '',
			'custname' => '',
			'num' => '',
			'signdt' => '',
			'money' => 0,
			'payee' => '',
			'payeeid' => ''
		];
	}
	
	/**
	 * 检查用户是否为管理员
	 * @param int $adminType
	 * @return bool
	 */
	private function isAdmin($adminType)
	{
		return in_array($adminType, [self::ADMIN_TYPE_SUPER, self::ADMIN_TYPE_ADMIN]);
	}
	
	public function paytypedata()
	{
		$data = array();
		$rows = $this->option->getmnum('paytype');
		foreach($rows as $k=>$rs){
			$data[] = array(
				'name' => $rs['name'],
				'value' => $rs['name'],
			);
		}
		return $data;
	}
	
	
	protected function storeafter($table, $rows)
	{
		$money 	 = 0;
		$hjfields	= arrvalue($this->flow->moders, 'hjfields');
		if($rows && isempt($hjfields)){
			foreach($rows as $k1=>$rs1){
				$money+=floatval($rs1['money']);
			}
			$carr['money'] 	= $this->rock->number($money); 
			$carr['htnum'] 	= '合计'; 
			$carr['id']		= 0;
			$rows[] = $carr;
		}
		$zhangarr = false;
		if($this->loadci==1 && $this->get('pnum')=='finall'){
			$zhangarr = m('fina')->getzhangtao();
			$zhangarrs= array();
			foreach($zhangarr as $k=>$rs){
				$zhangarrs[] = array('optgroup'=>'start','name'=>$rs['name']);
				$arows = m('fina')->getaccount($rs['value']);
				if($arows)foreach($arows as $k1=>$rs1){
					$zhangarrs[] = $rs1;
				}
				$zhangarrs[] = array('optgroup'=>'end','name'=>$rs['name']);
			}
			$zhangarr = $zhangarrs;
		}
		return array(
			'rows' => $rows,
			'zhangarr'=> $zhangarr
		);
	}
	
	public function createjizhangAjax()
	{
		$accountid 	= (int)$this->post('accountid','0');
		$id 		= (int)$this->post('id','0');
		$sm 		= $this->post('sm');
		$acrs 		= m('finount')->getone($accountid);
		
		
		$rs 		= m('custfina')->getone($id);
		$urs 		= m('admin')->getone($rs['uid']);
		$money 		= floatval($rs['money']);
		$jtype		= '销售收入';
		
		if($rs['type']=='1'){
			$jtype		= '购买材料';
			$money		= 0-$money;
		}
		
		$paydt = $rs['paydt'];
		if(isempt($paydt))$paydt = $rs['dt'];
		
		$uarr['comid'] 	= $rs['comid'];
		$uarr['type'] 	= $rs['type'];
		$uarr['money'] 	= $money;
		$uarr['custid'] 	= $rs['custid'];
		$uarr['custname'] = $rs['custname'];
		$uarr['applydt'] = $paydt;
		$uarr['optid'] = $this->adminid;
		$uarr['optname'] = $this->adminname;
		$uarr['optdt']   = $this->rock->now;
		$uarr['uid']     = $this->adminid;
		$uarr['xguid']   = $rs['uid'];
		$uarr['xgname'] 	= arrvalue($urs,'name');
		$uarr['xgdeptid'] 	= arrvalue($urs,'deptid');
		$uarr['xgdeptname'] 	= arrvalue($urs,'deptname');
		$uarr['explain'] 	= $rs['explain'].$sm;
		$uarr['accountid'] 	= $accountid;
		$uarr['zhangid'] 	= $acrs['zhangid'];
		$uarr['jtype'] 	= $jtype;
		
		$newid = m('finjibook')->insert($uarr);
		m('custfina')->update('jzid='.$newid.'', $id);
		
		return returnsuccess();
	}
}
			