<?php
class flow_workClassModel extends flowModel
{
	private $absfile = false;
	private $statearr;
	private $updatexm= false;

	public function initModel()
	{
		$this->statearr		 = c('array')->strtoarray('待执行|blue,已完成|green,执行中|#ff6600,终止|#888888,验证未通过|#9D4FF7');
	}
	
	//自定义审核人读取
	protected function flowcheckname($num){
		$sid = '';
		$sna = '';
		if($num=='run'){
			$sid = $this->rs['distid'];
			$sna = $this->rs['dist'];
		}
		return array($sid, $sna);
	}
	
	//转办时要更新对应的执行人员
	protected function flowcheckbefore(){
		$up = array();
		if($this->checkiszhuanyi==1){
			$up['dist'] 	= $this->rs['syszb_name'];
			$up['distid'] 	= $this->rs['syszb_nameid'];
			$up['status'] 	= 3; //待执行状态
		}
		if($up)$up['update'] = $up;
		return $up;
	}
	
	public function flowrsreplace($rs, $slx=0){
		
		$zts 		= $rs['status'];
		$str 		= $this->getstatus($rs,'','',1);
		if($slx>=1){
			$projectid 	= (int)$rs['projectid'];
			$rs['projectid'] = '';
			if($projectid>0){
				$prs 		= $this->db->getone('[Q]project', $projectid);
				if($prs){
					$rs['projectid']=$prs['title'];
				}
			}
		}
		if(!isempt($rs['enddt']) && !in_array($zts,array(1,2,5))){
			if(strtotime($rs['enddt'])<time())$rs['enddt'].='<font color=red>(已超时)</font>';
		}
		//$rs['status']= $str;
		if($rs['score']==0)$rs['score']='';
		if($rs['mark']==0)$rs['mark']='';
		if($slx==1){
			$zhuid = (int)arrvalue($rs,'zhuid','0');
			$nrs   = arrvalue($rs,'file_content');
			if($zhuid>0 && isempt($nrs)){
				$rs['file_content'] = m('file')->getstr($this->mtable, $zhuid, 3);
				$this->absfile = true;
			}
		}
		return $rs;
	}
	
	protected function flowgetfields($lx)
	{
		if($this->absfile){
			return array('file_content'=>'关联文件');
		}
	}
	
	protected function flowchangedata(){
		$this->rs['stateid'] = $this->rs['state'];
	}
	
	
	protected function flowdatalog($arr)
	{
		$isaddlog	= 0;
		$uid 		= $this->adminid;
		$ispingfen	= 0;
		$distid 	= ','.$this->rs['distid'].',';
		$zt 		= $this->rs['stateid'];
		if($this->contain($distid, ','.$this->adminid.',') && ($zt==0||$zt==2)){
			$isaddlog = 1;
		}
		
		$arr['isaddlog'] = $isaddlog; //是否可以添加日志记录
		$arr['state'] 	 = $this->rs['stateid'];
		
		//判断是否可以督导评分
		$where  = $this->ddwhere($uid);
		if($this->rows("`id`='$this->id' and `status`=1 and `mark`=0 $where")==1){
			$ispingfen		= 1;
		}
		$arr['ispingfen'] 	= $ispingfen; //是否可以评分
		$arr['score'] 		= $this->rs['score'];
		return $arr;
	}
	
	protected function flowsubmit($na, $sm)
	{
		//$this->push($this->rs['distid'], '', '[{type}]{title}');//提交给对应人提醒
		$this->push($this->rs['ddid'], '', '{optname}提交任务[{type}.{title}]分配给:{dist}，需要你督导','任务督导');//提醒给督导人员
		
		$zt  = 0;
		if(!isempt($this->rs['distid']))$zt = 3;//待执行的状态值
		$this->updatestatus($zt);
		if($this->updatexm)m('work')->updateproject($this->rs['projectid']);
	}
	
	protected function flowcheckfinsh($zt){
		if($this->updatexm)m('work')->updateproject($this->rs['projectid']);
		// 流程通过审核后，更新work表的enddt字段为flow_bill的updt时间
		if($zt == 1){ // 假设zt=1表示审核通过
			// 确保使用正确的mid和modeid来获取flow_bill信息
			$billrs = $this->billmodel->getone("`mid` = ".$this->id." AND `modeid` = ".$this->moders['id']."");
			if($billrs && !isempt($billrs['updt'])){
				// 更新work表的enddt字段，包含日期和时间
				$this->update(array('enddt' => $billrs['updt']), $this->id);
			}

			/*
			 * 自动创建电子服务单流程
			 * ------------------------------------------------------------
			 * 需求：流程审核通过后，根据工单信息自动创建 electwork 流程。
			 * 逻辑：
			 * 1. 仅当当前工单尚未关联电子服务单(electid=0) 时执行。
			 * 2. 组装需要写入 electwork 主表(goodm) 的字段。
			 * 3. 通过 flow 通用接口 querydata 创建并立即提交 electwork 流程。
			 * 4. 将返回的电子服务单 id 回写到当前工单 electid 字段。
			 */
			$electid = (int)arrvalue($this->rs, 'electid', 0);
			// 仅当工单满足条件(type=0 且 grade=1)时才创建电子服务单草稿
			$workType  = (int)arrvalue($this->rs,'type',-1);
			$workGrade = (int)arrvalue($this->rs,'grade',-1);

			if($electid==0 && $workType==0 && $workGrade==1){
				// 由于只有grade=1的工单才会自动生成electwork，所以status=0（保外服务单）
				$status = '0';
				
				// 根据项目的workqy值设置charge字段
				$charge = '0'; // 默认值
				$money = '0';  // 默认金额
				$projectId = (int)arrvalue($this->rs,'projectid',0);
				if($projectId > 0) {
					$projectInfo = m('project')->getone($projectId, 'workqy');
					if($projectInfo && !empty($projectInfo['workqy'])) {
						$workqyNum = trim($projectInfo['workqy']);
						// 根据workqy值查询option表获取对应的value值
						$optionInfo = m('option')->getone("`num`='$workqyNum'", 'value');
						if($optionInfo && !empty($optionInfo['value'])) {
							$charge = $optionInfo['value'];
							// 根据status设置初始金额
							if($status == '0') { // 保外服务单：使用charge作为初始金额
								$money = $charge;
							} else { // 保内服务单：金额为0
								$money = '0';
							}
						}
					}
				}
				
				// 组装 electwork 流程主表数据
				$edata = array(
					// 基本必填
					'type'      => 6,                 // 电子服务单固定 type=6
					'workid'    => $this->id,          // 关联工单

					// 申请人与操作人信息
					'uid'       => arrvalue($this->rs,'uid',0),

					// 项目/客户/联系人等信息 - 无效字段会被自动过滤
					'projectid' => arrvalue($this->rs,'projectid',0),
					'prname'    => arrvalue($this->rs,'prname'),
					'custid'    => arrvalue($this->rs,'custid',0),
					'custname'  => arrvalue($this->rs,'custname'),
					'linkname'  => arrvalue($this->rs,'linkname'),
					'linktel'   => arrvalue($this->rs,'linktel'),

					// 服务地点及时间
					'address'   => arrvalue($this->rs,'address'),
					'startdt'   => arrvalue($this->rs,'startdt'),
					'enddt'     => arrvalue($this->rs,'enddt'),

					// 执行人信息
					'distid'    => arrvalue($this->rs,'distid'),
					'dist'      => arrvalue($this->rs,'dist'),

					// 其他描述信息
					'fault'     => arrvalue($this->rs,'explain'),
					'process'   => arrvalue($this->rs,'workgc'),
					'num'       => arrvalue($this->rs,'num'),
					// 新增：图片上传
					'imglod'   => arrvalue($this->rs,'imglod'),
					'imgnew'    => arrvalue($this->rs,'imgnew'),
					// 新增：设置status、charge和money字段
					'status'    => $status,
					'charge'    => $charge,
					'money'     => $money,
					
					// 保存为草稿，不直接提交
					'isturn'    => 0
				);

				// 利用通用新增接口创建 electwork 流程
				$newElectId = m('flow')->querydata('electwork', $edata, '系统自动生成');

				if($newElectId>0){
					// 手动更新 workid 字段，确保电子服务单正确关联工单
					m('goodm')->update(array('workid'=>$this->id), $newElectId);

					// 回写 electid 到工单记录
					$this->update(array('electid'=>$newElectId), $this->id);

					// 初始化 electwork 流程对象
					$flowElect = m('flow')->initflow('electwork', $newElectId, false);
					$toIds     = arrvalue($flowElect->rs,'distid');

					// 生成编辑页面链接（PC 与 移动端）
					$editUrlPc      = URL.$flowElect->getinputurl('', $newElectId); // PC 端录入页
					$editUrlMobile  = $flowElect->rock->getouturl().'index.php?a=lum&m=input&d=flow&num=electwork&mid='.$newElectId;

					// 推送提醒给服务单执行人（草稿提醒）并携带编辑链接，设置 gname=流程申请 触发微信模板消息
					$params = array(
						'url'   => $editUrlPc,
						'wxurl' => $editUrlMobile,
					);
					$flowElect->push($toIds, '流程申请', '系统已为你创建电子服务单草稿，请及时完善并提交。', '电子服务单草稿提醒', 0, $params);

					// --- 额外发送微信公众号模板消息(流程待办) ---
					/* if(m('reim')->installwx(4)){
						$wxParams = array(
							'url'         => $editUrlMobile,
							'applyname'   => $this->adminname,
							'deptname'    => arrvalue($this->rs,'base_deptname'),
							'sericnum'    => $flowElect->sericnum,
							'title'       => '电子服务单草稿提醒',
							'modename'    => $flowElect->modename,
							'optname'     => $this->adminname,
							'optdt'       => $this->rock->now,
							'statustext'  => '待完善',
							'statustext_color' => '#FF0000',
							'applydt'     => $this->rock->date,
							'summary'     => $flowElect->getsummary(),
						);
						m('wxgzh:index')->send($toIds, '流程待办', $wxParams);
					} */
				}
			}
		}
	}

	
	protected function flowaddlog($a)
	{
		//提交报告时发送给创建人和督导人员
		if($a['name']=='进度报告'){
			$state 	= $a['status'];
			$arr['state'] = $state;
			$cont = ''.$this->adminname.'添加[{type}.{title}]的任务进度,说明:'.$a['explain'].'';
			if($state=='1')$cont='[{type}.{title}]任务'.$this->adminname.'已完成';
			$toid 	= $this->rs['optid'];
			$ddid	= $this->rs['ddid'];
			if(!isempt($ddid))$toid.=','.$ddid.'';
			$this->push($toid, '任务', $cont);
			$this->update($arr, $this->id);
		}
		if($a['name']=='指派给' || $a['name']=='转发'){
			$cname 	 = $this->rock->post('changename');
			$cnameid = $this->rock->post('changenameid');
			$state = '0';
			$arr['state'] 	= $state;
			$arr['distid'] 	= $cnameid;
			$arr['dist'] 	= $cname;
			$this->update($arr, $this->id);
			$this->push($cnameid, '任务', ''.$this->adminname.'指派任务[{type}.{title}]给你');
		}
		if($a['name'] == '任务评分'){
			$fenshu	 = (int)$this->rock->post('fenshu','0');
			$this->push($this->rs['distid'], '任务', ''.$this->adminname.'评分[{type}.{title}],分数('.$fenshu.')','任务评分');
			$this->update(array(
				'mark' => $fenshu
			), $this->id);
		}
	}
	
	private function ddwhere($uid)
	{
		$downid = m('admin')->getdown($uid, 1);
		$where  = 'and `ddid`='.$uid.'';
		if($downid!='')$where  = 'and (('.$uid.' in(`ddid`)) or (ifnull(`ddid`,\'0\')=\'0\' and `distid` in('.$downid.')) or (ifnull(`ddid`,\'0\')=\'0\' and `optid`='.$uid.'))';
		return $where;
	}
	
	protected function flowbillwhere($uid, $lx)
	{
		$where		= '';
		$projcetid 	= (int)$this->rock->post('projcetid');
		if($projcetid>0)$where='and `projectid`='.$projcetid.'';
		
		return array(
			'keywhere' => $where,
			'order' => '`optdt` desc'
		);
	}
	
	/**
	*	提醒快过期的任务
	*	$txsj 提前几天提醒
	*/
	public function tododay($txsj = 3) // 默认提前2小时提醒
	{
		$dtobj= c('date');
		$current_datetime = $this->rock->now(); // 获取当前完整日期时间
		$rows = $this->getrows("`status` in(0,3,4) and ifnull(`distid`,'')<>'' and `enddt`>='".$this->rock->date."'"); // 筛选条件中enddt比较时，仍用日期部分即可，避免遗漏当天截止的任务
		$arr  = array();
		foreach($rows as $k=>$rs){
			// 计算当前时间与截止日期的 小时 差
			$jg = $dtobj->datediff('h', $current_datetime, $rs['enddt'].' 23:59:59'); // 截止时间算到当天的最后一秒
			if($jg >= 0 && $jg <= $txsj){ // 任务尚未截止且在提醒时间范围内
				$dista = explode(',', $rs['distid']);
				foreach($dista as $distid){
					if(!isset($arr[$distid]))$arr[$distid] = array();
					$tis = ''.$jg.'小时后截止';
					if($jg == 0) $tis = '即将截止'; // 如果差值为0小时，表示在1小时内截止
					else if($jg < 24) $tis = ''.$jg.'小时后截止';
					else{
						$days_remaining = floor($jg / 24);
						$hours_remaining = $jg % 24;
						$tis = ''.$days_remaining.'天'.$hours_remaining.'小时后截止';
					}
					$arr[$distid][]= '['.$rs['type'].']'.$rs['title'].'('.$tis.');';
				}
			}
		}
		foreach($arr as $uid => $strarr){
			$this->flowweixinarr['url'] = $this->getwxurl();//设置微信提醒的详情链接
			$str = '';
			foreach($strarr as $k=>$str1){
				if($k>0)$str.="\n";
				$str.="".($k+1).".$str1";
			}
			if($str != '')$this->push($uid, '', $str, '任务到期提醒');
		}
	}
	
	//任务待办格式推送
	protected function flownexttodo($type)
	{
		if($type=='daiban'){
			return array(
				'cont' => '{title}\n任务类型：{type}\n是否保修：{grade}',
				'title'=> '任务待处理'
			);
		}
		
	}
	


}