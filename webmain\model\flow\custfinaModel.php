<?php
//客户收款单
class flow_custfinaClassModel extends flowModel
{
	public $statearrs,$statearrf;
	public $delbool = 0;
	public function initModel(){
		$this->statearrs		= c('array')->strtoarray('未收款|red,已收款|green');
		$this->statearrf		= c('array')->strtoarray('未付款|red,已付款|green');
	}
	
	public function flowrsreplace($rs)
	{
		if($rs['id']==0)return $rs;
		
		$starrr			= array('收','付');
		$rs['paystatus']	= $rs['ispay'];
		$ispay 			= '<font color=red>未'.$starrr[$rs['type']].'款</font>';
		if($rs['ispay']==1)$ispay = '<font color=green>已'.$starrr[$rs['type']].'款</font>';
		$rs['ispay']	 = $ispay;
		$rs['type']	 	 = ''.$starrr[$rs['type']].'款单';
		
		$htid			 = $rs['htid'];
		$url 	= '';
		// 处理E/e前缀的电子服务单ID
		if(preg_match('/^[eE](\d+)$/', $htid, $matches)){
			// 提取数字部分作为ID传递给getxiangurl方法
			// getxiangurl方法期望的是纯数字ID，而不是带前缀的格式
			$electId = intval($matches[1]);
			$url = $this->getxiangurl('electwork', $electId, 'auto');
		}
		else if($htid>0){
			// 先检查是否为电子服务单
			$electCheck = $this->db->getone('[Q]goodm', '`id`='.$htid.' and `type`=6', 'id');
			if($electCheck){
				// 是电子服务单，链接到电子服务单页面
				$url = $this->getxiangurl('electwork', $htid, 'auto');
			} else {
				// 是合同
				$url = $this->getxiangurl('custract', $htid, 'auto');
			}
		}
		if($htid<0)$url  = $this->getxiangurl('custxiao', 0-$htid, 'auto');
		if(arrvalue($rs,'xgid') && arrvalue($rs,'xgnum'))$url  = $this->getxiangurl($rs['xgnum'], $rs['xgid'], 'auto');
		if($url && !isempt($rs['htnum']))
			$rs['htnum'] = '<a href="javascript:;" onclick="js.open(\''.$url.'\')">'.$rs['htnum'].'</a>';
		
		if($rs['custid']>0){
			//$url  = $this->getxiangurl('customer', $rs['custid'], 'auto');
			//$rs['custname'] = '<a href="javascript:;" onclick="js.open(\''.$url.'\')">'.$rs['custname'].'</a>';
		}
		
		$jzid	= arrvalue($rs,'jzid');
		if($jzid>0){
			$url  = $this->getxiangurl('finjishou', $jzid, 'auto');
			$rs['jzid'] = '<a href="javascript:;" onclick="js.open(\''.$url.'\')">已生成</a>';
		}else if($jzid=='-1'){
			$rs['jzid'] = '<font color=#aaaaaa>不需要</font>';
		}else{
			$rs['jzid'] = '';
		}
		
		return $rs;
	}
	
	//操作菜单操作
	protected function flowoptmenu($ors, $arr)
	{
		//标识已付款处理
		if($ors['num']=='pay'){
			$ispay = 0;
			$paydt = arrvalue($arr,'fields_paydt', $this->rock->now);
			if(!isempt($paydt))$ispay = 1;
			$this->update("`ispay`='$ispay',`paydt`='$paydt'", $this->id);
			
			$htid = $this->rs['htid'];
			// 处理E前缀的电子服务单ID
			if(preg_match('/^[eE](\d+)$/', $htid, $matches)){
				$realId = intval($matches[1]);
				// 查询电子服务单的总金额
				$electrs = $this->db->getone('[Q]goodm', '`id`='.$realId.' and `type`=6', 'money');
				if($electrs) {
					$this->updateElectworkPayStatus($realId, floatval($electrs['money']));
				}
			}
			//处理正数ID，使用高效的类型判断
			else if((int)$htid > 0){
				$this->updateLinkedDocumentPayStatus((int)$htid);
			}
			//处理销售单
			else if((int)$htid<0){
				$salesId = 0 - (int)$htid;
				$htrs = m('goodm')->getone('`id`='.$salesId);
				if($htrs) {
					m('crm')->xiaozhuantai($htrs, 1);
					// 更新销售单的收款状态
					$this->updateSalesPayStatus($salesId, floatval($htrs['money']));
				}
			}
		}
		
		//复制一单
		if($ors['num']=='noupfuzhe'){
			$jine = $this->rock->number(trim($arr['sm']));
			$uarr = $this->getone($this->id);
			$money= $uarr['money'];
			unset($uarr['id']);
			$uarr['createname'] = $this->adminname;
			$uarr['createid']   = $this->adminid;
			$uarr['money']   	= $jine;
			$this->insert($uarr);
			$this->update('`money`=`money`-'.$jine.'', $this->id);
		}
	}
	
	//操作菜单操作之前
	protected function flowoptmenubefore($ors, $arr)
	{
		if($ors['num']=='noupfuzhe'){
			$sm = trim($arr['sm']);
			if(!$sm || !is_numeric($sm))return '输入"'.$sm.'"的不是金额';
			$sm = $this->rock->number($sm);
			if(floatval($sm)<=0)return '输入金额必须大于0';
			if(floatval($sm) >= floatval($this->rs['money']))return '输入的金额不能超过'.$this->rs['money'].'';
		}
	}
	
	protected function flowbillwhere($uid, $lx)
	{
		$month	= $this->rock->post('month');
		$where 	= '';
		if($month!=''){
			$where.=" and `dt` like '$month%'";
		}

		return array(
			'where' => $where,
			'order' => '`optdt` desc'
		);
	}
	
	protected function flowdeletebill($sm)
	{
		$this->delbool++;
		$xgid 	= arrvalue($this->rs,'xgid');
		$xgnum 	= arrvalue($this->rs,'xgnum');
		$htid 	= $this->rs['htid'];
		$sid 	= $this->id;
		if($xgnum && $xgid && $this->delbool==1){
			$sflow = m('flow:'.$xgnum.'')->initbase($xgnum);
			$drows = $this->getall("`xgnum`='$xgnum' and `xgid`='$xgid'");//相关联一起删
			foreach($drows as $k=>$rs1){
				$mid = $rs1['id'];
				$this->loaddata($mid, false);
				$this->deletebill($sm, false);
			}
			$sflow->update('`payid`=0', $xgid);
		}
		
		// 删除收款单后更新相关单据的收款状态
		if($htid){
			// 处理E前缀的电子服务单ID（支持大小写e/E）
			if(preg_match('/^[eE](\d+)$/', $htid, $matches)){
				$realId = intval($matches[1]);
				// 查询电子服务单的总金额
				$electrs = $this->db->getone('[Q]goodm', '`id`='.$realId.' and `type`=6', 'money');
				if($electrs) {
					$this->updateElectworkPayStatus($realId, floatval($electrs['money']));
				}
			}
			//处理正数ID，使用高效的类型判断
			else if((int)$htid > 0){
				$this->updateLinkedDocumentPayStatus((int)$htid);
			}
			//处理销售单
			else if((int)$htid<0){
				$salesId = 0-(int)$htid;
				$htrs = m('goodm')->getone('`id`='.$salesId);
				if($htrs) m('crm')->xiaozhuantai($htrs, 1);
			}
		}
		
		// 注释掉重新设置ID的代码，避免影响主表记录的删除
		// $this->id = $sid;
	}
	
	protected function flowgetoptmenu($opt,$bo=false)
	{
		if($opt=='noupcreatejz' && $bo){
			return m('mode')->iscun('finjishou');
		}
	}
	
	/**
	 * 更新关联单据的收款状态（合同或电子服务单）
	 * @param int $docId 文档ID
	 */
	private function updateLinkedDocumentPayStatus($docId){
		// 先检查是否为电子服务单（type=6）
		$electCheck = $this->db->getone('[Q]goodm', '`id`='.$docId.' and `type`=6', 'id,money');
		if($electCheck){
			// 是电子服务单
			$this->updateElectworkPayStatus($docId, floatval($electCheck['money']));
			return;
		}
		
		// 检查是否为合同
		$contractCheck = $this->db->getone('[Q]custract', '`id`='.$docId, 'id');
		if($contractCheck){
			// 是合同
			m('crm')->ractmoney($docId);
		}
	}
	
	/**
	 * 更新电子服务单收款状态
	 * @param int $electid 电子服务单ID
	 * @param float $totalMoney 电子服务单总金额（可选，避免重复查询）
	 */
	public function updateElectworkPayStatus($electid, $totalMoney = null)
	{
		if(!is_numeric($electid) || (int)$electid <= 0) return false;
		
		$electid = (int)$electid;
		
		// 如果没有传入总金额，则查询获取
		if(!$totalMoney){
			$electrs = $this->db->getone('[Q]goodm', '`id`='.$electid.' and `type`=6', 'money');
			if(!$electrs) return false;
			$totalMoney = floatval($electrs['money']);
		}
		
		// 计算已收款金额 - electwork模块只使用e123格式
		$shouMoney = $this->db->getall("SELECT money FROM [Q]custfina WHERE (`htid`='e".$electid."' or `htid`='E".$electid."') and `ispay`=1");
		
		$totalShou = 0;
		
		// 计算E前缀格式收款记录
		foreach($shouMoney as $sm){
			$totalShou += floatval($sm['money']);
		}
		
		// 更新ispay状态：0-未创建收款，1-已收全款，2-部分收款
		$ispay = 0; // 默认未创建收款
		if($totalShou > 0) {
			if($totalShou >= $totalMoney) {
				$ispay = 1; // 已收全款
			} else {
				$ispay = 2; // 部分收款
			}
		}
		$result = m('goodm')->update('`ispay`='.$ispay, $electid);
		
		return $result;
	}

	/**
	 * 更新销售单的收款状态
	 * @param int $salesid 销售单ID
	 * @param float $totalMoney 销售单总金额（可选，避免重复查询）
	 */
	public function updateSalesPayStatus($salesid, $totalMoney = null)
	{
		if(!is_numeric($salesid) || (int)$salesid <= 0) return false;
		
		$salesid = (int)$salesid;
		
		// 如果没有传入总金额，则查询获取
		if(!$totalMoney){
			$salesrs = $this->db->getone('[Q]goodm', '`id`='.$salesid.' and `type`=2', 'money');
			if(!$salesrs) return false;
			$totalMoney = floatval($salesrs['money']);
		}
		
		// 计算已收款金额 - 销售单使用负数ID格式
		$shouMoney = $this->db->getall("SELECT money FROM [Q]custfina WHERE `htid`='-".$salesid."' and `ispay`=1");
		
		$totalShou = 0;
		foreach($shouMoney as $sm){
			$totalShou += floatval($sm['money']);
		}
		
		// 更新ispay状态：0-未创建收款，1-已收全款，2-部分收款
		$ispay = 0; // 默认未创建收款
		if($totalShou > 0) {
			if($totalShou >= $totalMoney) {
				$ispay = 1; // 已收全款
			} else {
				$ispay = 2; // 部分收款
			}
		}
		$result = m('goodm')->update('`ispay`='.$ispay, $salesid);
		
		return $result;
	}
}