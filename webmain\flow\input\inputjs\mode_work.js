//初始函数
function initbodys(){
    //表单管理联动
// c.selectmap()
    // 获取目标input元素
    const inputElement = document.querySelector('input[name="linkname"]');

// 绑定click事件 - 添加空值检查
    if(inputElement) {
        inputElement.addEventListener('click', function(event) {
            autocomplete(this, 'contactsNameData');
        });
    }

    // 为prname字段添加项目搜索功能 - 传递给原生projectid控件
    const prnameElement = document.querySelector('input[name="prname"]');
    if(prnameElement) {
        // 移除原有的onclick事件，避免冲突
        prnameElement.removeAttribute('onclick');

        // 添加点击事件，触发原生项目选择控件
        prnameElement.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            triggerProjectSelect(this);
        });

        // 添加焦点事件，触发原生项目选择控件
        prnameElement.addEventListener('focus', function(event) {
            triggerProjectSelect(this);
        });
    }

    //获取客户名称 - 添加空值检查
    const input = document.querySelector('input[name="projectid"]');
    if(!input) {
        console.warn('[DEBUG] 未找到 name="projectid" 的输入框元素');
        return;
    }

// 获取原生 value 属性的描述符
    const descriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
// 重写 value 的 setter
    Object.defineProperty(input, 'value', {
        set: function(newValue) {
            const oldValue = this.value;
            descriptor.set.call(this, newValue); // 调用原生 setter
            if (oldValue !== newValue) {
                this.dispatchEvent(new Event('change')); // 手动触发事件
            }
        },
        get: function() {
            return descriptor.get.call(this); // 保持原生 getter
        }
    });

// 监听 change 事件
    input.addEventListener('change', function(e) {
        // 如果是从prname字段更新的，跳过处理避免循环
        if(this._isUpdatingFromPrname) {
            return;
        }

        if(typeof rockselectdata !== 'undefined'){
            rockselectdata['linkname'] = []; // 清空rockselect的缓存
        }
        // 清空联系人输入框
        var linknameInput = document.querySelector('input[name="linkname"]');
        if(linknameInput) linknameInput.value = '';
        var linktelInput = document.querySelector('input[name="linktel"]');
        if(linktelInput) linktelInput.value = '';

        // 获取项目地址信息
        var projectid = this.value;
        if(projectid && projectid !== '') {
            var gcan = {'pid': projectid};
            var url = js.getajaxurl('getprojectone', 'mode_work|input', 'flow', gcan);
            js.ajax(url, gcan, function(ret) {
                if(ret && ret.address) {
                    var addressInput = document.querySelector('input[name="address"]');
                    if(addressInput) addressInput.value = ret.address;
                }
                if(ret && ret.addresslatlng) {
                    var addresslatlngInput = document.querySelector('input[name="addresslatlng"]');
                    if(addresslatlngInput) addresslatlngInput.value = ret.addresslatlng;
                }
                // 设置客户ID
                if(ret && ret.custid) {
                    var custidInput = document.querySelector('input[name="custid"]');
                    if(custidInput) {
                        custidInput.value = ret.custid;
                        console.log('[DEBUG] 已设置客户ID：', ret.custid);
                    } else {
                        console.error('[DEBUG] 错误：未在页面中找到 name="custid" 的输入框元素。');
                    }
                }

                // 同步更新prname字段（避免循环更新）
                if(ret && ret.title) {
                    var prnameInput = document.querySelector('input[name="prname"]');
                    if(prnameInput && prnameInput.value !== ret.title) {
                        prnameInput.value = ret.title;
                    }
                }

                // 设置保修标记
                var gradeSelect = document.querySelector('select[name="grade"]');
                if(gradeSelect) {
                    gradeSelect.value = ret.grade_flag;
                } else {
                    console.error('[DEBUG] 错误：未在页面中找到 name="grade" 的下拉框元素。');
                }
                // 自动生成标题
                generateTitle(ret);
            }, 'get,json');
        }
    });
    
    // 生成标题函数
    function generateTitle(projectData) {
        var prname = projectData && projectData.title ? projectData.title : '';
        var typeInput = document.querySelector('input[name="type"], select[name="type"]');
        var typename = '';
        
        if(typeInput && typeInput.value) {
            // 获取类型名称
            var gcan = {'type': typeInput.value};
            var url = js.getajaxurl('getworktype', 'mode_work|input', 'flow', gcan);
            js.ajax(url, gcan, function(wt) {
                if(wt && wt.name) {
                    typename = wt.name;
                    var titleInput = document.querySelector('input[name="title"]');
                    if(titleInput && prname) {
                        titleInput.value = prname + typename;
                    }
                }
            }, 'get,json');
        } else {
            // 如果没有类型，只使用项目名称
            var titleInput = document.querySelector('input[name="title"]');
            if(titleInput && prname) {
                titleInput.value = prname + '工单';
            }
        }
    }
    
    // 监听类型变化，重新生成标题
    var typeInput = document.querySelector('input[name="type"], select[name="type"]');
    if(typeInput) {
        typeInput.addEventListener('change', function() {
            var projectidInput = document.querySelector('input[name="projectid"]');
            if(projectidInput && projectidInput.value) {
                var gcan = {'pid': projectidInput.value};
                var url = js.getajaxurl('getprojectone', 'mode_work|input', 'flow', gcan);
                js.ajax(url, gcan, function(ret) {
                    // 设置客户ID
                    if(ret && ret.custid) {
                        var custidInput = document.querySelector('input[name="custid"]');
                        if(custidInput) {
                            custidInput.value = ret.custid;
                            console.log('[DEBUG] 已设置客户ID：', ret.custid);
                        }
                    }
                    generateTitle(ret);
                }, 'get,json');
            }
        });
    }
    
    // 监听startdt变化，自动设置enddt
    var startdtInput = document.querySelector('input[name="startdt"]');
    if(startdtInput) {
        // 时间选择框通常在失去焦点时更新值，或者通过特定的API触发事件
        startdtInput.addEventListener('blur', function() { 
            setEndDateByStartDate(); // 修复：移除参数，函数内部自行获取值
        });
        // 如果时间选择框有特定的change或update事件，也可以监听那个事件
        // 例如： $(startdtInput).on('dp.change', function(e){ setEndDateByStartDate(e.date.format('YYYY-MM-DD HH:mm:ss')); });
    }
    
    // 根据startdt设置enddt的函数
    function setEndDateByStartDate(startdtValue) {
        if(!startdtValue) {
            console.log('[DEBUG] startdtValue 为空，不进行处理');
            return;
        }
        
        var enddtInput = document.querySelector('input[name="enddt"]');
        if(!enddtInput) {
            console.error('[DEBUG] 未找到 name="enddt" 的输入框元素');
            return;
        }

        try {
            // 解析startdt日期时间 (Y-m-d H:i:s)
            var parts = startdtValue.match(/(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/);
            if (!parts) {
                console.error('[DEBUG] startdtValue 格式不正确:', startdtValue);
                return;
            }
            // parts[0]是整个匹配的字符串, parts[1]是年, parts[2]是月, ..., parts[6]是秒
            var year = parseInt(parts[1], 10);
            var month = parseInt(parts[2], 10) - 1; // 月份是从0开始的
            var day = parseInt(parts[3], 10);
            var hours = parseInt(parts[4], 10);
            var minutes = parseInt(parts[5], 10);
            var seconds = parseInt(parts[6], 10);
            
            var startDate = new Date(year, month, day, hours, minutes, seconds);
            console.log('[DEBUG] 解析后的 startDate:', startDate);

            if(isNaN(startDate.getTime())) {
                console.error('[DEBUG] 解析 startDate 失败:', startdtValue);
                return;
            }
            
            // 获取当天日期，并将时间设为00:00:00
            var today = new Date(); 
            today.setHours(0, 0, 0, 0);
            
            // 获取startdt的日期部分，并将时间设为00:00:00
            var startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
            
            // 创建当天17:30:00的时间点
            var todayAt1730 = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate(), 17, 30, 0, 0);
            
            var endDate;
            
            // 判断startdt是否小于当天17:30:00
            if(startDate.getTime() < todayAt1730.getTime()) {
                // 设置为当天23:00:00
                endDate = new Date(startDateOnly);
                endDate.setHours(23, 0, 0, 0);
            } else {
                // 设置为第二天23:00:00
                endDate = new Date(startDateOnly);
                endDate.setDate(endDate.getDate() + 1);
                endDate.setHours(23, 0, 0, 0);
            }
            
            // 格式化为Y-m-d H:i:s格式
            var formattedEndDate = endDate.getFullYear() + '-'
                                 + String(endDate.getMonth() + 1).padStart(2, '0') + '-'
                                 + String(endDate.getDate()).padStart(2, '0') + ' '
                                 + String(endDate.getHours()).padStart(2, '0') + ':'
                                 + String(endDate.getMinutes()).padStart(2, '0') + ':'
                                 + String(endDate.getSeconds()).padStart(2, '0');
            
            // 设置enddt的值
            enddtInput.value = formattedEndDate;
            // 如果是特定的日期时间选择器，可能需要调用API来更新
            // 例如：$(enddtInput).val(formattedEndDate).trigger('change'); 
            // 或 $(enddtInput).data('DateTimePicker').date(moment(formattedEndDate));
            
        } catch(e) {
            console.error('[DEBUG] 设置结束时间时发生错误:', e);
        }
    }
    // 页面加载时根据当前时间规则赋值enddt
    setEndDateByCurrentTime();
    // 注意：上面已经添加了startdt的blur事件监听器，这里移除重复的代码
}

//表单联动
function autocomplete(o1,s1){
    // 添加全局变量检查
    if(typeof $ === 'undefined') {
        console.error('[DEBUG] jQuery ($) 未定义');
        return;
    }
    if(typeof jm === 'undefined') {
        console.error('[DEBUG] jm 对象未定义');
        return;
    }
    if(typeof mid === 'undefined') {
        console.error('[DEBUG] mid 变量未定义');
        return;
    }
    if(typeof js === 'undefined') {
        console.error('[DEBUG] js 对象未定义');
        return;
    }
    
    var a1 	= s1.split(',');
    const custid = $("input[name='custid']").val(); // 获取客户ID
    const projectid = $("input[name='projectid']").val(); // 获取项目ID
    var gcan = {
        'act':a1[0],
        'actstr':jm.base64encode(s1),
        'acttyle':'act',
        'sysmodenum':'contacts',
        'sysmid':mid,
        'custid':custid, // 传递客户ID
        'projectid':projectid // 传递项目ID
    };
    var url = js.getajaxurl('getselectdata','mode_contacts|input','flow',gcan);
    js.chajian('rockselect', {
        viewobj:o1,num:o1.name,limit:10,url:url,zb:0,strsss:s1,
        onitemclick:function(sna,val, d){
            var fid= this.nameobj.name;
            var a1 = this.strsss.split(',');
            // 使用d.value（纯姓名）设置输入框的值，而不是sna（带电话号码的显示名）
            this.nameobj.value = d.value || d.given_name_original || sna;
            if(a1[1])if(form(a1[1]))form(a1[1]).value = val
            c.onselectdataall(fid,d);
            $("input[name='linktel']").val(d.mobile)

        },
        nameobj:o1
    });
}

function setEndDateByCurrentTime() {
    var now = new Date();
    var compareDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 17, 30, 0);
    var endDate;
    if(now.getTime() < compareDate.getTime()) {
        endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 0, 0);
    } else {
        var nextDay = new Date(now.getFullYear(), now.getMonth(), now.getDate()+1, 23, 0, 0);
        endDate = nextDay;
    }
    var enddtStr = formatDateTime(endDate);
    var enddtInput = document.querySelector("input[name='enddt']");
    if(enddtInput) enddtInput.value = enddtStr;
}
function setEndDateByStartDate() {
    var startdtInput = document.querySelector("input[name='startdt']");
    var enddtInput = document.querySelector("input[name='enddt']");
    if(!startdtInput || !enddtInput) return;
    var startVal = startdtInput.value;
    if(!startVal) return;
    var reg = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/;
    var match = reg.exec(startVal);
    if(!match) {
        console.log('[DEBUG] startdt格式不正确:', startVal);
        return;
    }
    var y = parseInt(match[1]), m = parseInt(match[2])-1, d = parseInt(match[3]);
    var h = parseInt(match[4]), i = parseInt(match[5]), s = parseInt(match[6]);
    var startDate = new Date(y, m, d, h, i, s);
    var compareDate = new Date(y, m, d, 17, 30, 0);
    var endDate;
    if(startDate.getTime() < compareDate.getTime()) {
        endDate = new Date(y, m, d, 23, 0, 0);
    } else {
        var nextDay = new Date(y, m, d+1, 23, 0, 0);
        endDate = nextDay;
    }
    var enddtStr = formatDateTime(endDate);
    enddtInput.value = enddtStr;
}
function formatDateTime(dt) {
    var y = dt.getFullYear();
    var m = (dt.getMonth()+1).toString().padStart(2,'0');
    var d = dt.getDate().toString().padStart(2,'0');
    var h = dt.getHours().toString().padStart(2,'0');
    var i = dt.getMinutes().toString().padStart(2,'0');
    var s = dt.getSeconds().toString().padStart(2,'0');
    return y+'-'+m+'-'+d+' '+h+':'+i+':'+s;
}

// 触发原生项目选择控件
function triggerProjectSelect(prnameInput) {
    // 检查必要的全局变量
    if(typeof js === 'undefined') {
        console.error('[项目搜索] js 对象未定义');
        return;
    }

    // 查找projectid输入框
    var projectidInput = document.querySelector('input[name="projectid"]');
    if(!projectidInput) {
        console.error('[项目搜索] 未找到projectid输入框');
        return;
    }

    // 确保prname输入框可见且有正确的位置
    if(!prnameInput.offsetParent) {
        console.error('[项目搜索] prname输入框不可见');
        return;
    }

    // 获取prname输入框中的关键词
    var keyword = prnameInput.value.trim();

    // 构建项目数据源URL - 使用原生的projectdata方法（已修改支持搜索）
    var url = js.getajaxurl('projectdata', 'mode_work|input', 'flow');

    console.log('[项目搜索] 触发搜索，关键词:', keyword, '定位元素:', prnameInput);
    console.log('[项目搜索] 请求URL:', url);

    // 直接在新窗口中打开URL进行测试
    console.log('[项目搜索] 请在新窗口中打开以下URL进行测试:');
    console.log(url + '&key=');

    // 先测试URL是否正确
    console.log('[项目搜索] 测试AJAX请求...');
    $.ajax({
        type: 'GET',
        url: url,
        data: { key: '' },
        dataType: 'json',
        success: function(response) {
            console.log('[项目搜索] AJAX测试成功:', response);

            // 如果AJAX测试成功，再创建rockselect
            js.chajian('rockselect', {
                viewobj: prnameInput,  // 使用prname输入框作为定位基准
                num: 'prname_project_select',  // 使用唯一的标识符
                limit: 20,
                url: url,
                zb: 0,
                onitemclick: function(sna, val, d) {
                    // 设置项目ID到projectid字段
                    projectidInput.value = val;

                    // 设置项目名称到prname字段
                    prnameInput.value = d.name || sna;

                    // 触发projectid的change事件，让原有逻辑处理其他字段填充
                    projectidInput.dispatchEvent(new Event('change'));

                    console.log('[项目搜索] 已选择项目:', d);
                },
                nameobj: prnameInput,  // 使用prname输入框作为名称对象
                // 添加数据处理函数，确保数据格式正确
                ondatachuli: function(rows, ret) {
                    console.log('[项目搜索] 收到数据:', ret);
                    // 如果返回的是{rows: [...]}格式，提取rows
                    if(ret && ret.rows && Array.isArray(ret.rows)) {
                        console.log('[项目搜索] 使用ret.rows格式，数据量:', ret.rows.length);
                        return ret.rows;
                    }
                    // 如果直接是数组格式
                    if(Array.isArray(rows)) {
                        console.log('[项目搜索] 使用直接数组格式，数据量:', rows.length);
                        return rows;
                    }
                    console.log('[项目搜索] 数据格式不正确，返回空数组');
                    return [];
                }
            });
        },
        error: function(xhr, status, error) {
            console.error('[项目搜索] AJAX测试失败:', status, error);
            console.error('[项目搜索] 响应内容:', xhr.responseText);

            // 如果AJAX失败，尝试使用原生的projectdata方法
            console.log('[项目搜索] 尝试使用原生projectdata方法...');
            var fallbackUrl = js.getajaxurl('projectdata', 'mode_work|input', 'flow');
            console.log('[项目搜索] 备用URL:', fallbackUrl);

            js.chajian('rockselect', {
                viewobj: prnameInput,
                num: 'prname_project_select',
                limit: 20,
                url: fallbackUrl,
                zb: 0,
                onitemclick: function(sna, val, d) {
                    projectidInput.value = val;
                    prnameInput.value = d.name || sna;
                    projectidInput.dispatchEvent(new Event('change'));
                    console.log('[项目搜索] 已选择项目:', d);
                },
                nameobj: prnameInput
            });
        }
    });

    // 如果有关键词，自动在rockselect的搜索框中输入
    if(keyword) {
        // 等待rockselect界面显示后设置关键词
        setTimeout(function() {
            var rockselectInput = document.querySelector('#rockselectdiv input[type="input"]');
            if(rockselectInput) {
                rockselectInput.value = keyword;

                // 尝试调用rockselect的搜索方法
                if(typeof rockselectdata !== 'undefined' && rockselectdata['prname_project_select']) {
                    var rockselect = rockselectdata['prname_project_select'];
                    if(typeof rockselect.loaddata === 'function') {
                        rockselect.loaddata(keyword);
                    }
                }
            }
        }, 500);  // 增加延迟时间，确保rockselect完全初始化
    }
}

